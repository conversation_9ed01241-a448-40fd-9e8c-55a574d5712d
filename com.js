const fieldsConfig = [
    { field: "<EMAIL>", label: "Start Date", dataSource: "both" },
    { field: "<EMAIL>", label: "End Date", dataSource: "both" },
    { field: "<EMAIL>", label: "Athlete Subtype", dataSource: "both" },
    { field: "<EMAIL>", label: "Contact Affiliation Type", dataSource: "both" },
    { group: true, fields: [
        { field: "<EMAIL>", label: "Affiliated Organization/Federation", dataSource: "data1" },
        { field: "<EMAIL>", label: "", dataSource: "data2" }
    ]},
    { field: "<EMAIL>", label: "National Team", dataSource: "both" },
    { subgrid: "Sport Disciplines", fields: [
        { field: "ldv_firstname", label: "First Name", dataSource: "both" },
        { field: "ldv_sportname", label: "Sport Name", dataSource: "both" }
    ]}
];

function buildTable(data1, data2, ComparisonConfigrationEntity, recordId) {
    const table = document.getElementById("registrationWindowId");
    if (!table) {
        console.error("Table element not found");
        return;
    }
    
    table.innerHTML = "";
    const isArabic = isLanguageArabic();
    table.style.direction = isArabic ? "rtl" : "ltr";

    const thead = document.createElement("thead");
    thead.appendChild(createHeaderRow(isArabic, ComparisonConfigrationEntity));
    table.appendChild(thead);

    const tbody = document.createElement("tbody");
    fieldsConfig.forEach(item => {
        if (item.subgrid) {
            fetchSubGridRecords(item.subgrid, item.fields, tbody);
        } else {
            const row = item.group
                ? createGroupedRow(item, data1, data2, isArabic)
                : createRegularRow(item, data1, data2, isArabic);
            tbody.appendChild(row);
        }
    });
    table.appendChild(tbody);
}

function fetchSubGridRecords(subgridName, fields, tbody) {
    const formContext = Xrm.Page;
    const subGridControl = formContext.getControl(subgridName);
    if (subGridControl) {
        const grid = subGridControl.getGrid();
        const rows = grid.getRows();

        rows.forEach((row, index) => {
            const entity = row.getData().getEntity();
            fields.forEach(fieldItem => {
                const value = entity.attributes.get(fieldItem.field)?.getValue() || "N/A";
                const modifiedFieldItem = { ...fieldItem, label: `${fieldItem.label} (${index + 1})`, value };
                const rowElement = createRegularRow(modifiedFieldItem, {}, {}, isLanguageArabic());
                tbody.appendChild(rowElement);
            });
        });
    } else {
        console.error("Subgrid not found: " + subgridName);
    }
}


function fetchSubGridRecords(subgridName, fields, tbody) {
    const formContext = Xrm.Page;
    const subGridControl = formContext.getControl(subgridName);
    if (subGridControl) {
        const grid = subGridControl.getGrid();
        const rows = grid.getRows();

        rows.forEach((row, index) => {
            const entity = row.getData().getEntity();
            const data1 = {};
            const data2 = {};

            fields.forEach(fieldItem => {
                const value = entity.attributes.get(fieldItem.field)?.getValue() || "N/A";

                // Populate data1 and data2 as expected
                if (fieldItem.dataSource === "both" || fieldItem.dataSource === "data1") {
                    data1[fieldItem.field] = value;
                }
                if (fieldItem.dataSource === "both" || fieldItem.dataSource === "data2") {
                    data2[fieldItem.field] = value;
                }

                const modifiedFieldItem = { 
                    ...fieldItem, 
                    label: `${fieldItem.label} (${index + 1})` 
                };

                const rowElement = createRegularRow(modifiedFieldItem, data1, data2, isLanguageArabic());
                tbody.appendChild(rowElement);
            });
        });
    } else {
        console.error("Subgrid not found: " + subgridName);
    }
}

